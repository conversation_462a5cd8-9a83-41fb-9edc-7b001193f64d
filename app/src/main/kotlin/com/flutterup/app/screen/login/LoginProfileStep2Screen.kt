@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.login

import android.widget.Space
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.text.LabelSize
import com.flutterup.app.design.text.LabelText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PinkSecondary
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.screen.HomeBaseRoute
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.login.state.LoginProfileStep2UIState
import com.flutterup.app.screen.login.vm.LoginProfileStep2ViewModel


@Composable
fun LoginProfileStep2Screen() {
    val navController = LocalNavController.current
    val viewModel: LoginProfileStep2ViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LoginProfileStep2Content(
        uiState = uiState,
        onSkipClick = { navController.navigate(HomeBaseRoute) },
    )
}

@Composable
private fun LoginProfileStep2Content(
    uiState: LoginProfileStep2UIState,
    onBackClick: () -> Unit = {},
    onSkipClick: () -> Unit = {},
) {
    AppScaffold(
        title = { },
        canGoBack = false, //不允许返回
        onBackClick = onBackClick,
        modifier = Modifier.fillMaxSize(),
        rightNavigationContent = {
            LoginProfileStep2Skip(
                modifier = Modifier.width(50.dp).height(25.dp),
                onClick = onSkipClick
            )
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
            )

            Column (
                modifier = Modifier
                    .fillMaxSize()
                    .padding(it)
                    .padding(horizontal = 16.dp)
            ) {
                LoginProfileStep2Photo()
            }
        }
    }
}

@Composable
private fun LoginProfileStep2Skip(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier.fillMaxSize().wrapContentHeight(),
        shape = RoundedCornerShape(30.dp),
        border = BorderStroke(1.dp, PurplePrimary),
        contentPadding = PaddingValues(top = 0.dp, bottom = 2.dp),
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = Color.Transparent,
            contentColor = PurplePrimary,
            disabledContainerColor = Color.Transparent,
            disabledContentColor = PurplePrimary,
        )
    ) {
        LabelText(
            text = stringResource(R.string.skip),
            size = LabelSize.Large,
            color = PurplePrimary
        )
    }
}

@Composable
private fun LoginProfileStep2Photo() {
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        verticalArrangement = Arrangement.spacedBy(10.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp),
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        items(
            count = 5,
            span = { if (it == 0) GridItemSpan(2) else GridItemSpan(1) }
        ) { index ->
            when(index) {
                0 -> LoginProfileStep2PhotoItem(
                    modifier = Modifier.aspectRatio(1f),
                    isMainPhoto = true
                )

                1 -> Column(
                    modifier = Modifier
                ) {
                    LoginProfileStep2PhotoItem(
                        modifier = Modifier.aspectRatio(1f),
                        isMainPhoto = false
                    )
                    
                    Spacer(modifier = Modifier.height(10.dp))

                    LoginProfileStep2PhotoItem(
                        modifier = Modifier.aspectRatio(1f),
                        isMainPhoto = false
                    )
                }

                else -> LoginProfileStep2PhotoItem(
                    modifier = Modifier.aspectRatio(1f),
                    isMainPhoto = false
                )
            }
        }
    }
}

@Composable
private fun LoginProfileStep2PhotoItem(
    modifier: Modifier = Modifier,
    isMainPhoto: Boolean = false,
) {
    val stroke = Stroke(
        width =  with(LocalDensity.current) { 2.dp.toPx() },
        pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
    )

    val shape = RoundedCornerShape(20.dp)

    Box(
        modifier = modifier
            .background(
                color = PurplePrimary.copy(alpha = 0.21f),
                shape = shape
            )
    ) {
        Box(
            modifier = modifier
                .background(
                    brush = itemLinearGradient,
                    shape = shape
                )
                .then(
                    if (isMainPhoto) {
                        Modifier.drawBehind {
                            drawRoundRect(
                                color = PurplePrimary,
                                style = stroke,
                                cornerRadius = CornerRadius(20.dp.toPx())
                            )
                        }
                    } else {
                        Modifier
                    }
                )
        ) {
            Spacer(
                modifier = Modifier
                    .matchParentSize()
                    .background(
                        brush = itemVerticalGradient,
                        shape = shape
                    )
            )
        }
    }
}


@Preview
@Composable
private fun LoginProfileComplete2Preview() {
    AppTheme {
        LoginProfileStep2Content(
            uiState = LoginProfileStep2UIState()
        )
    }
}

private val itemLinearGradient = Brush.linearGradient(
    colors = listOf(
        PinkSecondary,
        Color.White.copy(alpha = 0.4f)
    )
)

private val itemVerticalGradient = Brush.verticalGradient(
    colors = listOf(
        PurplePrimary.copy(alpha = 0.21f),
        Color.Transparent
    )
)