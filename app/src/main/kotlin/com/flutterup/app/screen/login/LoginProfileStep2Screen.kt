@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.login

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.LinearGradient
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.text.LabelSize
import com.flutterup.app.design.text.LabelText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PinkSecondary
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.screen.HomeBaseRoute
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.login.state.LoginProfileStep2UIState
import com.flutterup.app.screen.login.vm.LoginProfileStep2ViewModel


@Composable
fun LoginProfileStep2Screen() {
    val navController = LocalNavController.current
    val viewModel: LoginProfileStep2ViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LoginProfileStep2Content(
        uiState = uiState,
        onSkipClick = { navController.navigate(HomeBaseRoute) },
    )
}

@Composable
private fun LoginProfileStep2Content(
    uiState: LoginProfileStep2UIState,
    onBackClick: () -> Unit = {},
    onSkipClick: () -> Unit = {},
) {
    AppScaffold(
        title = { },
        canGoBack = false, //不允许返回
        onBackClick = onBackClick,
        modifier = Modifier.fillMaxSize(),
        rightNavigationContent = {
            LoginProfileStep2Skip(
                modifier = Modifier.width(50.dp).height(25.dp),
                onClick = onSkipClick
            )
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
            )

            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(it)
            ) {
                item { LoginProfileStep2Photo() }
            }
        }
    }
}

@Composable
private fun LoginProfileStep2Skip(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier,
        shape = RoundedCornerShape(30.dp),
        border = BorderStroke(1.dp, PurplePrimary),
        contentPadding = PaddingValues(top = 0.dp, bottom = 2.dp),
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = Color.Transparent,
            contentColor = PurplePrimary,
            disabledContainerColor = Color.Transparent,
            disabledContentColor = PurplePrimary,
        )
    ) {
        LabelText(
            text = stringResource(R.string.skip),
            size = LabelSize.Large,
            color = PurplePrimary
        )
    }
}

@Composable
private fun LoginProfileStep2Photo() {
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        verticalArrangement = Arrangement.spacedBy(10.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp),
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        for (i in 0..5) {
            item(
                key = i,
                span = { if (i == 0) GridItemSpan(3) else GridItemSpan(1) }
            ) {

            }
        }
    }
}

@Composable
private fun LoginProfileStep2PhotoItem(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier.background(
            brush = itemLinearGradient,
            shape = RoundedCornerShape(20.dp)
        )
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_common_top_bg),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize()
        )
    }
}


@Preview
@Composable
private fun LoginProfileComplete2Preview() {
    AppTheme {
        LoginProfileStep2Content(
            uiState = LoginProfileStep2UIState()
        )
    }
}

private val itemLinearGradient = Brush.linearGradient(
    colors = listOf(
        PinkSecondary,
        Color.White
    )
)